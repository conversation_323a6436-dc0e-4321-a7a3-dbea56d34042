package com.swhd.agent.service.account.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swhd.magiccube.core.annotation.Lockable;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.agent.api.account.dto.param.detail.AccountOceanengineCustomerRechargeDetailListParam;
import com.swhd.agent.api.account.dto.param.detail.AccountOceanengineCustomerRechargeDetailPageParam;
import com.swhd.agent.api.account.dto.param.detail.AccountOceanengineCustomerSelfRechargeBySourceIdParam;
import com.swhd.agent.api.account.dto.param.detail.AccountOceanengineCustomerSelfRechargeByTitleParam;
import com.swhd.agent.api.account.dto.result.AccountOceanengineCustomerRechargeDetailResult;
import com.swhd.agent.service.account.entity.AccountOceanengineCustomerRechargeDetail;
import com.swhd.agent.service.account.mapper.AccountOceanengineCustomerRechargeDetailMapper;
import com.swhd.agent.service.account.service.AccountOceanengineCustomerRechargeDetailService;
import com.swhd.agent.service.account.entity.AccountOceanengineCustomerRechargeSource;
import com.swhd.agent.service.account.entity.AccountOceanengineInfo;
import com.swhd.agent.service.account.entity.AccountOceanengineCustomerRechargeCredit;
import com.swhd.agent.service.account.entity.AccountOceanengineCustomerCreditChangeRecord;
import com.swhd.agent.service.account.service.AccountOceanengineCustomerRechargeSourceService;
import com.swhd.agent.service.account.service.AccountOceanengineInfoService;
import com.swhd.agent.service.account.service.AccountOceanengineCustomerRechargeCreditService;
import com.swhd.agent.service.account.service.AccountOceanengineCustomerCreditChangeRecordService;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.mp.util.SortOrderUtil;
import com.swj.magiccube.tool.bean.BeanUtil;
import com.swj.magiccube.util.WebUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 自助充值明细表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Service
@AllArgsConstructor
public class AccountOceanengineCustomerRechargeDetailServiceImpl
        extends BaseHdServiceImpl<AccountOceanengineCustomerRechargeDetailMapper, AccountOceanengineCustomerRechargeDetail>
        implements AccountOceanengineCustomerRechargeDetailService {

    private final AccountOceanengineCustomerRechargeSourceService accountOceanengineCustomerRechargeSourceService;
    private final AccountOceanengineInfoService accountOceanengineInfoService;
    private final AccountOceanengineCustomerRechargeCreditService accountOceanengineCustomerRechargeCreditService;
    private final AccountOceanengineCustomerCreditChangeRecordService accountOceanengineCustomerCreditChangeRecordService;

    @Override
    public IPage<AccountOceanengineCustomerRechargeDetailResult> page(AccountOceanengineCustomerRechargeDetailPageParam param) {
        AccountOceanengineCustomerRechargeDetailListParam listParam = BeanUtil.copy(param, AccountOceanengineCustomerRechargeDetailListParam.class);
        Page<AccountOceanengineCustomerRechargeDetail> page = convertToPage(param);
        if (Func.isNotEmpty(param.getSort())) {
            List<OrderItem> orders = SortOrderUtil.normalize(param.getSort()).stream()
                    .map((item) -> (new OrderItem()).setColumn(item.getProperty()).setAsc(item.getAscending()))
                    .toList();
            page.setOrders(orders);
        }else{
            page.setOrders(List.of(new OrderItem().setColumn("execute_time").setAsc(false),
                    new OrderItem().setColumn("id").setAsc(false)));
        }
        return baseMapper.pageWithAgent(page, listParam);
    }

    @Override
    public List<AccountOceanengineCustomerRechargeDetailResult> list(AccountOceanengineCustomerRechargeDetailListParam param) {
        return baseMapper.listWithAgent(param);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lockable(prefixKey = "agent:account:oceanengine:customer:recharge:detail:selfRecharge", key = "#param.advertiserId", waitTime = 6000)
    public Rsp<Void> selfRechargeBySourceId(AccountOceanengineCustomerSelfRechargeBySourceIdParam param) {
        // 1. 根据sourceId获取唯一开启的充值源
        AccountOceanengineCustomerRechargeSource source = accountOceanengineCustomerRechargeSourceService.lambdaQuery()
                .eq(AccountOceanengineCustomerRechargeSource::getId, param.getSourceId())
                .eq(AccountOceanengineCustomerRechargeSource::getState, 1) // 1-开启
                .limitOne();

        if (source == null) {
            return RspHd.fail("充值源不存在或已关闭");
        }

        return processSelfRecharge(source, param.getAdvertiserId(), param.getRechargeAccountAmount(),
                param.getInputMethod(), param.getCapitalType());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lockable(prefixKey = "agent:account:oceanengine:customer:recharge:detail:selfRecharge", key = "#param.advertiserId", waitTime = 6000)
    public Rsp<Void> selfRechargeByTitle(AccountOceanengineCustomerSelfRechargeByTitleParam param) {
        // 1. 根据title前缀获取唯一开启的充值源
        List<AccountOceanengineCustomerRechargeSource> sources = accountOceanengineCustomerRechargeSourceService.lambdaQuery()
                .likeRight(AccountOceanengineCustomerRechargeSource::getTitle, param.getTitle())
                .eq(AccountOceanengineCustomerRechargeSource::getState, 1) // 1-开启
                .list();

        if (sources.isEmpty()) {
            return RspHd.fail("未找到匹配的开启状态充值源");
        }

        if (sources.size() > 1) {
            return RspHd.fail("找到多个匹配的充值源，请使用更精确的标题前缀");
        }

        AccountOceanengineCustomerRechargeSource source = sources.getFirst();
        return processSelfRecharge(source, param.getAdvertiserId(), param.getRechargeAccountAmount(),
                param.getInputMethod(), param.getCapitalType());
    }

    /**
     * 处理自助充值的核心业务逻辑
     *
     * @param source 充值源
     * @param advertiserId 广告主ID
     * @param rechargeAccountAmount 充值金额
     * @param inputMethod 录入方式
     * @param capitalType 转账类型
     * @return 处理结果
     */
    private Rsp<Void> processSelfRecharge(AccountOceanengineCustomerRechargeSource source, Long advertiserId,
                                         BigDecimal rechargeAccountAmount, String inputMethod, String capitalType) {
        // 2. 根据广告主ID获取账户信息，进而获取客户ID
        AccountOceanengineInfo accountInfo = accountOceanengineInfoService.getByAdvertiserId(advertiserId);
        if (accountInfo == null) {
            return RspHd.fail("广告主账户不存在");
        }

        Long customId = accountInfo.getCustomId();
        if (customId == null) {
            return RspHd.fail("广告主账户未关联客户");
        }

        // 3. 判断广告主是否属于source配置的客户
        if (source.getCustomerIds() == null || !source.getCustomerIds().contains(customId)) {
            return RspHd.fail("该广告主不在充值源配置的客户范围内");
        }

        // 4. 根据客户ID查询授信额度
        AccountOceanengineCustomerRechargeCredit credit = accountOceanengineCustomerRechargeCreditService.lambdaQuery()
                .eq(AccountOceanengineCustomerRechargeCredit::getCustomId, customId)
                .limitOne();

        if (credit == null || !Integer.valueOf(1).equals(credit.getAllowSelfRecharge())) {
            return RspHd.fail("客户未开启自助充值功能");
        }

        // 5. 校验五分钟内不能重复充值
        LocalDateTime fiveMinutesAgo = LocalDateTime.now().minusMinutes(5);
        long recentRechargeCount = lambdaQuery()
                .eq(AccountOceanengineCustomerRechargeDetail::getAdvertiserId, advertiserId)
                .ge(AccountOceanengineCustomerRechargeDetail::getCreateTime, fiveMinutesAgo)
                .count();

        if (recentRechargeCount > 0) {
            return RspHd.fail("五分钟内不能重复发起同一个广告主的自助充值");
        }

        // 6. 计算充值现金金额
        //todo 待确认 渠道返点是否允许为null 或为 0
        // rechargeCashAmount = rechargeAccountAmount / (1 + channelRebate/100)
        BigDecimal channelRebate = accountInfo.getChannelRebate();
        if (channelRebate == null) {
            channelRebate = BigDecimal.ZERO;
        }
        BigDecimal divisor = BigDecimal.ONE.add(channelRebate.divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP));
        BigDecimal rechargeCashAmount = rechargeAccountAmount.divide(divisor, 2, RoundingMode.HALF_UP);

        // 7. 校验剩余授信额度是否大于等于rechargeCashAmount
        BigDecimal creditLimit = credit.getCreditLimit() != null ? credit.getCreditLimit() : BigDecimal.ZERO;
        BigDecimal usedAmount = credit.getUsedAmount() != null ? credit.getUsedAmount() : BigDecimal.ZERO;
        BigDecimal beforeRemainingAmount = creditLimit.subtract(usedAmount);

        if (beforeRemainingAmount.compareTo(rechargeCashAmount) < 0) {
            return RspHd.fail("剩余授信额度不足，当前剩余额度：" + beforeRemainingAmount + "，需要额度：" + rechargeCashAmount);
        }

        // 8. 计算变更前后的剩余量
        BigDecimal afterRemainingAmount = beforeRemainingAmount.subtract(rechargeCashAmount); // 变更后剩余量

        // 9. 更新已使用额度
        BigDecimal newUsedAmount = usedAmount.add(rechargeCashAmount);
        credit.setUsedAmount(newUsedAmount);
        accountOceanengineCustomerRechargeCreditService.updateById(credit);

        // 10. 创建充值明细记录
        AccountOceanengineCustomerRechargeDetail detail = new AccountOceanengineCustomerRechargeDetail()
                .setAdvertiserId(advertiserId)
                .setCustomId(customId)
                .setSourceId(source.getId())
                .setRechargeAccountAmount(rechargeAccountAmount)
                .setRechargeCashAmount(rechargeCashAmount)
                .setChannelRebate(channelRebate)
                .setCreditLimit(creditLimit)
                .setRemainingCashLimit(afterRemainingAmount)
                .setInputMethod(inputMethod)
                .setCapitalType(capitalType)
                .setTraceId(WebUtil.getTraceId())
                .setExecuteStatus("PENDING") // 待处理
                .setExecuteTime(LocalDateTime.now());

        boolean result = save(detail);

        if (result) {
            // 11. 记录授信额度变更记录
            AccountOceanengineCustomerCreditChangeRecord changeRecord = new AccountOceanengineCustomerCreditChangeRecord()
                    .setSelfRechargeCreditId(credit.getId())
                    .setChangeType("CONSUME") // 客户消耗
                    .setBeforeAmount(beforeRemainingAmount)
                    .setAfterAmount(afterRemainingAmount)
                    .setChangeAmount(rechargeCashAmount)
                    .setRechargeDetailId(detail.getId());
            accountOceanengineCustomerCreditChangeRecordService.save(changeRecord);
        }
        return RspHd.status(result);
    }

    //处理 待处理充值

}
